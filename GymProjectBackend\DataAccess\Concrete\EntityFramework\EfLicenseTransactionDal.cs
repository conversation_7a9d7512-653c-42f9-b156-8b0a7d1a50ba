using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfLicenseTransactionDal : EfEntityRepositoryBase<LicenseTransaction, GymContext>, ILicenseTransactionDal
    {
        // Constructor injection (Scalability için)
        public EfLicenseTransactionDal(GymContext context) : base(context)
        {
        }

        // Backward compatibility constructor
        public EfLicenseTransactionDal() : base()
        {
        }

        public IDataResult<List<LicenseTransaction>> GetAllFiltered(int? userID, string startDate, string endDate, int page, int pageSize)
        {
            try
            {
                List<LicenseTransaction> transactions;

                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    transactions = _context.LicenseTransactions.Where(lt => lt.IsActive).ToList();
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        transactions = context.LicenseTransactions.Where(lt => lt.IsActive).ToList();
                    }
                }

                // Apply filters
                if (userID.HasValue)
                {
                    transactions = transactions.Where(lt => lt.UserID == userID.Value).ToList();
                }

                if (!string.IsNullOrEmpty(startDate) && DateTime.TryParse(startDate, out DateTime start))
                {
                    transactions = transactions.Where(lt => lt.TransactionDate >= start).ToList();
                }

                if (!string.IsNullOrEmpty(endDate) && DateTime.TryParse(endDate, out DateTime end))
                {
                    transactions = transactions.Where(lt => lt.TransactionDate <= end.AddDays(1)).ToList();
                }

                // Sort by TransactionDate descending (most recent first)
                transactions = transactions.OrderByDescending(lt => lt.TransactionDate).ToList();

                // Apply pagination
                var totalCount = transactions.Count;
                var paginatedTransactions = transactions
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return new SuccessDataResult<List<LicenseTransaction>>(paginatedTransactions, $"Toplam {totalCount} kayıt bulundu");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<LicenseTransaction>>($"Lisans işlemleri getirilirken hata oluştu: {ex.Message}");
            }
        }

        public IResult AddLicenseTransaction(LicenseTransaction licenseTransaction)
        {
            try
            {
                licenseTransaction.CreationDate = DateTime.Now;
                licenseTransaction.IsActive = true;

                Add(licenseTransaction);
                return new SuccessResult("Lisans işlemi başarıyla eklendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Lisans işlemi eklenirken hata oluştu: {ex.Message}");
            }
        }

        public IResult SoftDeleteLicenseTransaction(int id)
        {
            try
            {
                var transaction = Get(lt => lt.LicenseTransactionID == id);
                if (transaction == null)
                {
                    return new ErrorResult("Lisans işlemi bulunamadı");
                }

                transaction.IsActive = false;
                transaction.DeletedDate = DateTime.Now;

                Update(transaction);
                return new SuccessResult("Lisans işlemi başarıyla silindi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Lisans işlemi silinirken hata oluştu: {ex.Message}");
            }
        }

        public IDataResult<List<LicenseTransaction>> GetAllOrderedByDate()
        {
            try
            {
                var transactions = GetAll(lt => lt.IsActive)
                    .OrderByDescending(lt => lt.TransactionDate)
                    .ToList();

                return new SuccessDataResult<List<LicenseTransaction>>(transactions);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<LicenseTransaction>>($"Lisans işlemleri getirilirken hata oluştu: {ex.Message}");
            }
        }

        public IDataResult<object> GetTotalAmountsByPaymentMethod()
        {
            try
            {
                var transactions = GetAll(lt => lt.IsActive);

                var totals = new
                {
                    TotalAmount = transactions.Sum(t => t.Amount),
                    TotalCash = transactions.Where(t => t.PaymentMethod.Contains("Nakit")).Sum(t => t.Amount),
                    TotalCreditCard = transactions.Where(t => t.PaymentMethod.Contains("Kredi Kartı")).Sum(t => t.Amount),
                    TotalTransfer = transactions.Where(t => t.PaymentMethod.Contains("Havale")).Sum(t => t.Amount)
                };

                return new SuccessDataResult<object>(totals);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<object>($"Toplam tutarlar hesaplanırken hata oluştu: {ex.Message}");
            }
        }

        public IDataResult<object> GetMonthlyRevenueByYear(int year)
        {
            try
            {
                var transactions = GetAll(lt => lt.IsActive && lt.TransactionDate.Year == year);

                var monthlyData = new List<decimal>();
                for (int month = 1; month <= 12; month++)
                {
                    var monthlyTotal = transactions
                        .Where(t => t.TransactionDate.Month == month)
                        .Sum(t => t.Amount);
                    monthlyData.Add(monthlyTotal);
                }

                var result = new
                {
                    Year = year,
                    MonthlyRevenues = monthlyData
                };

                return new SuccessDataResult<object>(result);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<object>($"Aylık gelir verileri hesaplanırken hata oluştu: {ex.Message}");
            }
        }
    }

}
