using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfOperationClaimDal : EfEntityRepositoryBase<OperationClaim, GymContext>, IOperationClaimDal
    {
        // Constructor injection (Scalability için)
        public EfOperationClaimDal(GymContext context) : base(context)
        {
        }

        // Backward compatibility constructor
        public EfOperationClaimDal() : base()
        {
        }

        // SOLID prensiplerine uygun: Business logic DAL katmanında
        public IDataResult<OperationClaim> GetOperationClaimByNameWithValidation(string name)
        {
            try
            {
                // Sadece "member" rolü için çalışacak şekilde sınırlandırma
                if (name != "member")
                {
                    return new ErrorDataResult<OperationClaim>(null, "Bu metot sadece 'member' rolü için kullanılabilir");
                }

                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var operationClaim = _context.OperationClaims.FirstOrDefault(o => o.Name == name);

                    // IP adresi ve zaman damgası gibi bilgileri loglama
                    var ipAddress = System.Net.Dns.GetHostName();
                    var timestamp = DateTime.Now;
                    Console.WriteLine($"[{timestamp}] GetByName called for role '{name}', IP: {ipAddress}");

                    return new SuccessDataResult<OperationClaim>(operationClaim);
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        var operationClaim = context.OperationClaims.FirstOrDefault(o => o.Name == name);

                        // IP adresi ve zaman damgası gibi bilgileri loglama
                        var ipAddress = System.Net.Dns.GetHostName();
                        var timestamp = DateTime.Now;
                        Console.WriteLine($"[{timestamp}] GetByName called for role '{name}', IP: {ipAddress}");

                        return new SuccessDataResult<OperationClaim>(operationClaim);
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<OperationClaim>(null, $"OperationClaim alınırken hata oluştu: {ex.Message}");
            }
        }
    }

}
